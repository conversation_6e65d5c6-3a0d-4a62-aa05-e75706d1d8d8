import { TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID } from '@/config';
import { ASSISTANT_ROLE, CHAT_PLACEHOLDER, CHAT_ROLE, USER_ROLE } from '@/config/chat';
import { AuthModelState } from '@/models/auth';
import { ChatModelState } from '@/models/chat';
import { GreetingRender } from '@/pages/Chat/components/Greeting';
import {
  deleteDataId,
  feedBackContentById,
  getChatStreamApiUrl,
  imageTaskAbort,
  uploadApi,
} from '@/services/chat';
import type { FetchChatStreamParams } from '@/services/types';
import {
  dispatchInUtils,
  ErrorMessageType,
  generateRandomString,
  getAccessToken,
  getState,
  getTenantId,
  parseImgContentData,
  processFlowNodeStatusText,
} from '@/utils';
import eventBus from '@/utils/eventBus';
import { Chat, Toast } from '@douyinfe/semi-ui';
import type {
  Message,
  RenderActionProps,
  RenderContentProps,
  RenderInputAreaProps,
} from '@douyinfe/semi-ui/lib/es/chat/interface';
import type { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import rehypeMathJax from 'rehype-mathjax';
import remarkMath from 'remark-math';
import { useLocation, useParams, useSearchParams, useSelector } from 'umi';
import { navItems } from '../Office/navitems';
import {
  ChatBottomSlot,
  CustomChatContentRender,
  CustomHintBoxRender,
  CustomInputRender,
} from './components/ChatCustom';
import RenderChatAction from './components/RenderChatAction';
import styles from './index.less';
import type { ChatInstanceRef, ChatProps, ImgContentData, QuestionsData } from './types';

export interface Attachment {
  type: 'file' | 'image';
  name: string;
  url: string;
}

const uploadProps = {
  // accept: '', 文档类限制
  action: process.env.API_URL + uploadApi,
};
const uploadTipProps = {
  content: '最多10个，支持pdf、txt、docx、doc、xlsx、xls、pptx、ppt、py、java、js、ts等',
};

const parseEventStream = async (
  reader: any,
  onData: (content: string | ImgContentData[], type: string) => void,
  onDone: (content?: string) => void,
  onFlowResponses: (flowResponses: any) => void,
) => {
  const decoder = new TextDecoder();
  let buffer = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      // 处理可能存在的多事件块
      const chunks = buffer.split('\n\n');
      buffer = chunks.pop() || '';

      for (const chunk of chunks) {
        const [eventLine, dataLine] = chunk.split('\n');
        const event = {
          event: eventLine.split(':')[1],
          data: dataLine.split(':').slice(1).join(':'),
        };

        if (event.event === 'flowResponses') {
          const flowResponses = JSON.parse(event.data);
          onFlowResponses(flowResponses);
        }

        if (event.event === 'answer_json') {
          const result = JSON.parse(event.data || '');
          onData(result, 'answer_json');
          onDone();
        }

        if (event.event === 'image') {
          const result = JSON.parse(event.data || '');
          onData(parseImgContentData(result), 'image');
          onDone();
        }

        if (event.event === 'DONE') {
          onDone();
        }
        if (event.event === 'error') {
          const eventData = JSON.parse(event.data);

          const errorMsg =
            eventData?.errorCode === 1 ? eventData.errorMsg : ErrorMessageType.streamError;
          onDone(errorMsg);
          return;
        }

        if (event.event === 'flowNodeStatus') {
          const flowNodeStatus = JSON.parse(event.data || '{}');
          const result = {
            ...flowNodeStatus,
            name: processFlowNodeStatusText(flowNodeStatus?.name || '', flowNodeStatus),
          };
          onData(result, 'progress');
        }

        if (event.event === 'flowNodeStatus') {
          onData('', 'loading');
        }

        if (['answer', 'fastAnswer'].includes(event.event) && event.data) {
          try {
            const jsonData = JSON.parse(event.data);
            const choices = jsonData.choices?.[0] || {};
            // const finish_reason = choices.finish_reason || '';
            // if (finish_reason === 'stop') {
            // onDone();
            // return;
            // }
            // 推理内容
            const reasoningContent = choices.delta?.reasoning_content || '';
            if (reasoningContent) onData(reasoningContent, 'reasoning');
            // 回答内容
            const content = choices.delta?.content || '';
            if (content) onData(content, 'answer');
          } catch (e) {
            console.error('JSON parse error:', e);
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
};

const useStreamingChat = (
  userId: string | number | undefined,
  appCode: string = 'chat',
  inputAreaRenderRef: any,
) => {
  const [searchParams] = useSearchParams();
  const { chatId = '' } = useParams<{ chatId: string }>() || searchParams.get('chatId');
  const location = useLocation();
  const routeId = (location.state as QuestionsData)?.routeId || searchParams.get('code');
  const secondCode = searchParams.get('code') || '';
  const imageConfig = useSelector((state: any) => state.chat.imageConfig);
  const isOnline = (location.state as QuestionsData)?.isOnlineState;
  useEffect(() => {
    dispatchInUtils({
      type: 'chat/setOnlineStatus',
      payload: isOnline ? isOnline : false,
    });
  }, []);

  // 取消请求
  const cancelRequest = () => {
    const abortController = getState().chat.chatMsg![chatId]?.abortController;
    // 获取最后一个AI回复的消息
    const lastAssistantMessage = getState().chat.chatMsg![chatId]?.messages.findLast(
      (item: any) => item.role === ASSISTANT_ROLE,
    );
    // 文生图中断
    if (appCode === 'image' && lastAssistantMessage?.resChatDataId) {
      imageTaskAbort({
        chatId,
        resChatDataId: lastAssistantMessage.resChatDataId,
      });
    }
    if (abortController) {
      abortController.abort();
      dispatchInUtils({
        type: 'chat/updateAbortController',
        payload: {
          appCode,
          chatId,
          abortController: null,
        },
      });
    }
  };

  // 获取对话所有参数
  const getChatParams = (
    content: any,
    attachment: FileItem[],
    reqChatDataId: string,
    resChatDataId: string,
    extra?: any,
  ) => {
    let params: FetchChatStreamParams = {
      search: getState().chat.isOnline,
      userId,
      reqChatDataId,
      resChatDataId,
      chatId,
      appCode,
      content,
      fileTypes: attachment,
    };

    // 有文件时不允许联网查询
    if (attachment.length > 0) {
      params.search = false;
    }

    if (secondCode && secondCode !== 'undefined') {
      params.routeId = secondCode;
    }

    if (['engin', 'image'].includes(appCode) && routeId) {
      params.routeId = routeId;
    }
    const isImageRoute =
      TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID.image.includes(routeId + '') && appCode === 'image';

    const index = getState().chat.chatMsg[chatId]?.messages?.findLastIndex(
      (item: any) => item?.role === USER_ROLE,
    );

    const paramsJson =
      index > -1 ? getState().chat.chatMsg[chatId]?.messages?.[index].paramsJson : {};

    console.log(1, 'paramsJson=', paramsJson);

    const paramsObj = extra?.sendFlag === 'reset' ? paramsJson : imageConfig;

    console.log('imageConfig参数', imageConfig);
    console.log('extra参数', extra);

    if (isImageRoute) {
      params.batchSize = paramsObj?.batchSize;
      params.color = paramsObj?.color;
      params.ratio = paramsObj?.ratio;
      params.style = paramsObj?.style;
      params.userInput = extra?.userInput; // 发送消息和重置取值问题验证一下
      params.content = content;
    }
    return params;
  };

  const sendStreamRequest = async (
    content: string,
    attachment: FileItem[],
    reqChatDataId: string,
    resChatDataId: string,
    extra?: any,
    onProgress: (content: string, type: string) => void,
    setPending: (pending?: boolean) => void,
    onFlowResponses: (flowResponses: any) => void,
    onDone?: () => void,
  ) => {
    dispatchInUtils({
      type: 'chat/updateAbortController',
      payload: {
        appCode,
        chatId,
        abortController: new AbortController(),
      },
    });

    const signal = getState().chat.chatMsg![chatId]?.abortController?.signal;
    setPending(true);

    const chatApiUrl = getChatStreamApiUrl({ appCode });
    const data = getChatParams(content, attachment, reqChatDataId, resChatDataId, extra);
    const tenantId = getTenantId();
    // 清空文生图的比例参数等配置
    inputAreaRenderRef.current?.imgConfigBtnReset();
    try {
      const response = await fetch(chatApiUrl, {
        method: 'POST',
        headers: {
          'tenant-id': tenantId,
          'content-type': 'application/json',
          Authorization: `Bearer ${getAccessToken()}`,
        },
        body: JSON.stringify(data),
        signal,
      });

      if (!response) throw new Error('No response data');

      const reader = response.body!.getReader();
      await parseEventStream(
        reader,
        onProgress,
        () => {
          // 回复完成done
          setPending(false);
          dispatchInUtils({
            type: 'chat/updateAbortController',
            payload: {
              appCode,
              chatId,
              abortController: null,
            },
          });

          dispatchInUtils({
            type: 'chat/updateButtonParams',
            payload: {
              chatId,
              imageButtonParams: imageConfig,
            },
          });
          /* 因影响下次输入暂时注释
          dispatch({
            type: 'chat/setImageConfig',
            payload: {},
          });
          数据流完全结束后获取提示列表
          dispatchInUtils({
            type: 'chat/fetchHints',
            payload: {
              appCode,
              chatId,
            },
          });
          */
          onDone?.();
        },
        onFlowResponses,
      );
    } catch (e: any) {
      setPending(false);
      dispatchInUtils({
        type: 'chat/updateAbortController',
        payload: {
          appCode,
          chatId,
          status: e.message.includes('aborted') ? 'complete' : 'error',
          abortController: null,
        },
      });
    }
  };

  return { sendStreamRequest, cancelRequest };
};

const AIChat = forwardRef<ChatInstanceRef, ChatProps>((chatProps, ref) => {
  const {
    isTopSlot = false,
    placeholder,
    appCode = 'chat',
    customHandleSend,
    customHandleSendReading,
    chatTopSlot,
    showTemplateHeader = false,
    initialInputContent = '',
    customChatFunction,
    vditorTitle = '',
    smarPlaceholder,
  } = chatProps;
  const { user } = useSelector((state: { auth: AuthModelState }) => state.auth);
  const { chatMsg } = useSelector((state: { chat: ChatModelState }) => state.chat);
  const chatRef = useRef<any>();
  const inputAreaRenderRef = useRef<any>();
  const { sendStreamRequest, cancelRequest } = useStreamingChat(
    user?.id,
    appCode,
    inputAreaRenderRef,
  );
  const [inputContent, setInputContent] = useState(initialInputContent || '');
  const isChange = useRef(false);
  const { chatId = '' } = useParams<{ chatId: string }>();
  const [showTemplate, setShowTemplate] = useState(false);
  const imageConfig = useSelector((state: any) => state.chat.imageConfig);

  // 监听initialInputContent变化，更新inputContent
  useEffect(() => {
    if (initialInputContent !== undefined) {
      setInputContent(initialInputContent);
    }
  }, [initialInputContent]);

  // 初始化时根据appCode设置showTemplate状态
  useEffect(() => {
    const result = navItems.filter((item) => item.key === appCode);
    if (result.length > 0) {
      setShowTemplate(true);
    }
  }, [appCode]);

  useEffect(() => {
    if (!chatMsg![chatId]?.pending) {
      let newMessages = chatMsg![chatId]?.messages;
      if (!newMessages?.length || !newMessages) return;
      const lastMsg = newMessages[newMessages.length - 1];
      if (lastMsg?.role === ASSISTANT_ROLE) {
        newMessages = [...newMessages.slice(0, -1), { ...lastMsg, status: 'complete' }];
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            appCode,
            [chatId]: {
              ...chatMsg![chatId],
              messages: newMessages,
            },
          },
        });
        // 确保模板显示
        setShowTemplate(true);
      } else {
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            appCode,
            [chatId]: {
              ...chatMsg![chatId],
              messages: newMessages,
            },
          },
        });
      }
    }
  }, [chatMsg![chatId]?.pending]);

  const chatRoleConfig = {
    ...CHAT_ROLE,
    user: {
      name: user?.name,
      avatar: user?.avatar,
    },
  };

  // 获取最后一个用户消息
  const getLastUserMessage = (): Message => {
    const chatMessages = getState().chat.chatMsg![chatId]?.messages;
    const lastUserMessageIndex = chatMessages.findLastIndex((item: any) => item.role === USER_ROLE);
    const lastUserMessage = lastUserMessageIndex > -1 ? chatMessages[lastUserMessageIndex] : {};
    return lastUserMessage;
  };

  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );
  const handleMessageSend = async (
    originalContent: string,
    attachment?: FileItem[],
    extra?: any,
  ) => {
    // 拼接后的最新的content, 文生图的时候拼接上比例等参数
    const content = getNewContent(originalContent, extra);

    const randomChatId = generateRandomString();
    const reqChatDataId = extra?.reqChatDataId || `req_${randomChatId}`;
    const resChatDataId = extra?.resChatDataId || `res_${randomChatId}`;
    let userContent: any = [];

    // 首先添加文本内容
    if (content) {
      userContent.push({
        type: 'text',
        text: content,
      });
    }

    // 添加文件内容
    if (attachment?.length) {
      attachment.forEach((item: any) => {
        const attachFile: any = {
          type: item.type === 'image' ? 'image_url' : 'file_url',
          text: item.name,
        };
        if (item.type === 'image') {
          attachFile.image_url = {
            url: item.url,
          };
        } else {
          attachFile.file_url = {
            url: item.url,
            transferUrl: item?.transferUrl,
            name: item.name,
            size: item.size,
            type: item.type,
            fileTags: item.fileTags || [],
          };
        }
        userContent.push(attachFile);

        if (pageMode && pageMode === 'doc' && attachFile) {
          let fileExt = '';
          if (attachFile.file_url) {
            let exttmps = attachFile.file_url.url.split('?')[0].split('.');
            fileExt = exttmps[exttmps.length - 1];
          }

          if (fileExt && ['pdf', 'doc', 'docx', 'ppt', 'pptx'].indexOf(fileExt) > -1) {
            dispatchInUtils({
              type: 'pdfContainer/changeUrl',
              payload: {
                url: attachFile.file_url.url,
                name: attachFile.file_url.name,
                size: attachFile.file_url.size,
              },
            });
          }
        }
      });
    }

    // 如果没有内容，使用空字符串
    if (userContent.length === 0) {
      userContent = content || '';
    }

    // 添加用户消息
    const userMessage = {
      role: 'user',
      id: reqChatDataId,
      createAt: Date.now(),
      content: userContent,
      appCode,
      reqChatDataId,
      resChatDataId,
    };
    const assistantMessage = {
      role: ASSISTANT_ROLE,
      id: resChatDataId,
      createAt: Date.now(),
      content: '',
      status: 'loading' as Message['status'],
      appCode,
      reqChatDataId,
      resChatDataId,
    };
    const newMessages: Message[] = getState().chat.chatMsg![chatId]?.messages;
    const lastIndex = newMessages && newMessages.findLastIndex((item) => item.role === USER_ROLE);
    if (lastIndex > -1) {
      newMessages[lastIndex].isEdit = false;
    }

    if (isChange.current) {
      dispatchInUtils({
        type: 'chat/saveMessage',
        payload: {
          appCode,
          [chatId]: {
            ...chatMsg![chatId],
            messages: [
              ...newMessages.slice(0, -1),
              { ...newMessages.at(-1), reqChatDataId, resChatDataId },
              assistantMessage,
            ],
          },
        },
      });
      isChange.current = false;
    } else {
      // 创建对话
      try {
        if (appCode === 'image') {
          userMessage.paramsJson = imageConfig;
        }
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            appCode,
            [chatId]: {
              ...getState().chat.chatMsg![chatId],
              messages: [...newMessages, userMessage, assistantMessage],
            },
          },
        });
      } catch (e) {}
    }

    // 立即显示模板
    setShowTemplate(true);
    // 需要传入原始用户输入的content（不含文生图中的比例等参数）
    const extraParams = { ...(extra || {}) };
    if (extra?.sendFlag === 'reset') {
      const lastUserMessage = getLastUserMessage();
      extraParams.userInput = lastUserMessage?.paramsJson?.userInput;
    } else {
      extraParams.userInput = originalContent;
    }

    await sendStreamRequest(
      content,
      attachment || [],
      reqChatDataId,
      resChatDataId,
      extraParams,
      (chunk, type) => {
        dispatchInUtils({
          type: 'chat/chunkMessage',
          payload: {
            chunk,
            type,
            chatId,
          },
        });
      },
      (pending) => {
        dispatchInUtils({
          type: 'chat/updatePending',
          payload: {
            chatId,
            pending,
          },
        });
        // 如果结束响应，再次确保模板显示
        if (!pending) {
          setShowTemplate(true);
        }
      },
      (flowResponses: any[]) => {
        dispatchInUtils({
          type: 'chat/saveFlowResponses',
          payload: {
            flowResponses,
            chatId,
          },
        });
      },
      () => {
        setTimeout(() => {
          chatRef.current?.scrollToBottom(false);
        }, 500);
      },
    );

    // 重置文生图的比例参数等配置
    // inputAreaRenderRef.current?.imgConfigBtnReset();
  };

  /**
   * 获取最新的content
   * @param content 用户输入的content
   * @returns 最新的content，文生图的时候拼接上比例等参数
   */
  const getNewContent = (content: string, extra?: any) => {
    if (extra?.sendFlag === 'reset') {
      return content;
    }
    const concatSymbol = content && imageConfig?.configText ? '，' : '';
    // 处理消息重连的时候，目前只有appCode为image才有重连
    if (extra?.sendFlag === 'reconnect') {
      return getLastUserMessage()?.paramsJson?.content || '';
    }

    if (appCode === 'image') {
      return `${content}${concatSymbol}${imageConfig?.configText || ''}`;
    }
    return content;
  };

  // 检查content、attachment以及文生图中比例等参数（imageConfig.configText）是否都为空
  const isEmptyContentAndFile = (
    content: string = '',
    attachment: FileItem[] = [],
    extra?: any,
  ) => {
    const isReAction = ['reset', 'reconnect'].includes(extra?.sendFlag);
    if (isReAction) return false;
    // 如果image 检查三者都为空，imageConfig.configText 是文生图操作参数，在重置、重连操作的不用判断
    if (appCode === 'image') {
      return !content?.trim() && attachment.length === 0 && !imageConfig.configText;
    }
    return !content?.trim() && attachment.length === 0;
  };

  // content 一直是用户输入的内容，也就是userInput（不含包拼接比例等参数部分）
  const handleSend = useCallback(
    (content: string = '', attachment: FileItem[] = [], extra?: any) => {
      const isImageAppReconnect = appCode === 'image' && extra?.sendFlag === 'reconnect';
      if (
        isEmptyContentAndFile(content, attachment, extra) ||
        (!isImageAppReconnect && getState().chat.chatMsg![chatId]?.pending)
      ) {
        return false;
      }
      // 确保模板可见
      setShowTemplate(true);
      // 从智能体对话页面发送消息触发
      if (customHandleSend) {
        customHandleSend(content, attachment, imageConfig);
        return;
      }
      if (customHandleSendReading) {
        customHandleSendReading(content, attachment);
      }

      // 在历史对话的时候发送消息触发
      handleMessageSend(content, attachment, extra);
      // inputAreaRenderRef.current?.imgConfigBtnReset();
    },
    [handleMessageSend],
  );

  const handleChatsChange = useCallback(
    (chats?: Message) => {
      if (!chats) return;
      if (chatRef.current) {
        chatRef.current.containerRef.current!.style.display = 'none';
      }
      dispatchInUtils({
        type: 'chat/saveMessage',
        payload: {
          appCode,
          [chatId]: {
            ...getState().chat.chatMsg![chatId],
            messages: chats,
          },
        },
      });
      // 如果最后一个消息是AI, 并且value的length为0, 则重新建立链接
      const lastMessage = chats.at(-1);
      console.log('是否重连', lastMessage?.role === ASSISTANT_ROLE && lastMessage?.isReconnect);

      if (lastMessage?.role === ASSISTANT_ROLE && lastMessage?.isReconnect) {
        console.log('重新建立链接', lastMessage);
        handleChatReconnect(lastMessage);
        lastMessage.isReconnect = false;
      }
      // const isLastAnswer = chats.at(-1)?.role === ASSISTANT_ROLE;
      // if (isLastAnswer && !getState().chat.chatMsg![chatId]?.pending) {
      //   dispatchInUtils({
      //     type: 'chat/fetchHints',
      //     payload: { chatId, appCode },
      //   });
      // }
      setTimeout(() => {
        if (chatRef.current) {
          chatRef.current.containerRef.current!.style.display = 'block';
          chatRef.current?.scrollToBottom(false);
        }
      }, 0);
    },
    [chatId],
  );

  /**
   * 消息重连
   */
  const handleChatReconnect = (msg?: Message) => {
    let reqChatDataId = msg?.reqChatDataId;
    let resChatDataId = msg?.resChatDataId;
    if (!msg?.reqChatDataId) {
      reqChatDataId = `req_${msg?.id?.substring(4)}`;
      resChatDataId = `res_${msg?.id?.substring(4)}`;
    }
    // 先删除
    const newMessages: Message[] = getState().chat.chatMsg![chatId]?.messages;
    const lastMessage: Message[] = newMessages.slice(0, -1);

    dispatchInUtils({
      type: 'chat/saveMessage',
      payload: {
        appCode,
        [chatId]: {
          ...chatMsg![chatId],
          messages: lastMessage,
        },
      },
    });

    const { content } = lastMessage.at(-1)!;

    console.log(1, 'getLastUserMessage()=', getLastUserMessage());

    const originalContent = getLastUserMessage()?.paramsJson?.userInput || '';
    console.log('重连 originalContent =', getLastUserMessage()?.paramsJson);

    isChange.current = true;
    console.log(1, '重连时content=', content);
    if (Array.isArray(content)) {
      let postMessage = '';
      const fileList: any[] = [];
      content.forEach((item: any) => {
        if (item.type === 'text') {
          postMessage = item?.text || '';
        } else if (item.type === 'image_url') {
          fileList.push({
            type: 'image',
            name: item?.image_url?.name || item?.text,
            url: item.image_url.url,
          });
        } else if (item.type === 'file_url') {
          fileList.push({
            type: 'file',
            name: item.file_url.name,
            url: item.file_url.url,
            size: item.file_url.size || '0KB',
            fileTags: item.file_url?.fileTags || [],
          });
        }
      });
      console.log(2, '重连时postMessage=', postMessage);
      // 重连的时候必须需要文件参数
      handleSend(originalContent, fileList, {
        reqChatDataId,
        resChatDataId,
        sendFlag: 'reconnect',
      });
    } else {
      console.log(3, '是否会走到这里重连时content=', content);

      handleSend(content!, [], {
        reqChatDataId,
        resChatDataId,
        sendFlag: 'reconnect',
      });
    }
  };
  /**
   * 重置消息时触发
   */
  const resetMessage = (msg?: Message) => {
    const data: any = {
      appCode,
      chatId,
    };
    let reqChatId = msg?.reqChatDataId;
    let resChatId = msg?.resChatDataId;
    if (!msg?.reqChatDataId) {
      reqChatId = `req_${msg?.id?.substring(4)}`;
      resChatId = `res_${msg?.id?.substring(4)}`;
    }
    // 调用删除接口
    deleteDataId({
      ...data,
      dataType: 1,
      dataId: reqChatId,
    });
    deleteDataId({
      ...data,
      dataType: 2,
      dataId: resChatId,
    });
    const newMessages: Message[] = getState().chat.chatMsg![chatId]?.messages;
    const lastMessage: Message[] = newMessages.slice(0, -1);

    dispatchInUtils({
      type: 'chat/saveMessage',
      payload: {
        appCode,
        [chatId]: {
          ...chatMsg![chatId],
          messages: lastMessage,
        },
      },
    });
    const { content } = lastMessage.at(-1)!;
    isChange.current = true;
    if (Array.isArray(content)) {
      let postMessage = '';
      const fileList: any[] = [];
      content.forEach((item: any) => {
        if (item.type === 'text') {
          postMessage = item?.text || '';
        } else if (item.type === 'image_url') {
          fileList.push({
            type: 'image',
            name: item?.image_url?.name || item?.text,
            url: item.image_url.url,
          });
        } else if (item.type === 'file_url') {
          fileList.push({
            type: 'file',
            name: item.file_url.name,
            url: item.file_url.url,
            size: item.file_url.size || '0KB',
            fileTags: item.file_url?.fileTags || [],
          });
        }
      });
      handleSend(postMessage, fileList, { sendFlag: 'reset' });
    } else {
      handleSend(content!);
    }
  };

  /**
   * 删除消息时触发
   */
  const onMessageDelete = async (msg?: Message) => {
    const data: any = {
      appCode,
      chatId,
      dataType: 1,
      dataId: msg?.id,
    };
    const lastMsg = getState().chat.chatMsg![chatId]?.messages.at(-1);
    if (msg?.role === ASSISTANT_ROLE) {
      data.dataType = 2;
      if (msg?.resChatDataId) {
        data.dataId = msg?.resChatDataId;
      }
    } else if (msg?.reqChatDataId) {
      data.dataId = msg?.reqChatDataId;
    }
    try {
      // 删除指定对话
      const res = await deleteDataId(data);
      if (res.data) {
        const newMessages: Message[] = getState().chat.chatMsg![chatId]?.messages.filter(
          (item: any) => item.id !== msg?.id,
        );
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            appCode,
            [chatId]: {
              ...chatMsg![chatId],
              messages: newMessages,
            },
          },
        });
      }
      if (lastMsg?.id === msg?.id && msg?.role === ASSISTANT_ROLE) {
        dispatchInUtils({
          type: 'chat/clearHints',
          payload: { chatId },
        });
      }
    } catch (err: any) {
      Toast.error(err.msg || '操作失败');
    }
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    handleMessageSend,
    handleChatReconnect,
    clearMessages: () =>
      dispatchInUtils({
        type: 'chat/saveMessage',
        payload: {
          appCode,
          [chatId]: {
            messages: [],
          },
        },
      }),
    getMessages: () => chatMsg![chatId]?.messages as Message[],
    handleChatsChange: (messages: Message[]) => handleChatsChange(messages),
    setShowTemplate: (value: boolean) => setShowTemplate(value),
    getInputAreaRenderRef: () => inputAreaRenderRef.current?.clearSmartList(),
  }));

  // 使用eventBus发布信息
  const eventBusHandleMessageSend = async (e: any) => {
    let { content, attachment } = e;
    if (!content) return;
    if (!attachment) attachment = [];
    if (chatRef.current) chatRef.current.scrollToBottom();
    await new Promise((resolve) => {
      setTimeout(resolve, 1000);
    });
    handleMessageSend(content, attachment);

    setTimeout(() => {
      if (!chatRef.current) return;
      chatRef.current.scrollToBottom();
    }, 1000);
  };

  useEffect(() => {
    eventBus.on('Chat-Chat-HandleMessageSend', eventBusHandleMessageSend);
    return () => {
      eventBus.off('Chat-Chat-HandleMessageSend', eventBusHandleMessageSend);
    };
  }, []);

  // 处理模板选择
  const handleTemplateSelect = (template: any) => {
    if (template && template.message) {
      setInputContent(template.message);
    }
  };

  // 处理返回操作
  // const handleBack = () => {
  //   navigate(-1);
  // };

  // 控制模板头部显示

  // 关闭模板头部
  const handleCloseTemplate = () => {
    setShowTemplate(false);
  };

  // 判断是否需要显示模板头部
  // 修改为用户发送消息后立即显示模板
  const shouldShowTemplateHeader = () => {
    // 只检查是否允许显示模板，其他条件在组件内部控制
    const result = navItems.filter((item) => item.key === appCode);
    return result.length > 0 || showTemplateHeader;
  };
  const customInputAreaRender = useCallback(
    (props?: RenderInputAreaProps) => {
      const propsData: any = {
        ...props,
        chatRef,
        chatTopSlot,
        initialInputContent: inputContent,
        customChatFunction,
        appCode,
        showTemplateHeader: shouldShowTemplateHeader() && showTemplate,
        onSelectTemplate: handleTemplateSelect,
        onCloseTemplate: handleCloseTemplate,
        smarPlaceholder,
      };
      return <CustomInputRender ref={inputAreaRenderRef} {...propsData} />;
    },
    [chatTopSlot, inputContent, showTemplate, shouldShowTemplateHeader, chatId, chatMsg],
  );

  const customChatActionRender = useCallback(
    (props: RenderActionProps) => {
      const propsData = {
        ...props,
        appCode,
        chatId,
        chatRef,
      };
      return <RenderChatAction {...propsData} />;
    },
    [chatRef],
  );

  const customChatContentRender = useCallback(
    (props: RenderContentProps) => {
      const propsData = {
        ...props,
        chatId,
        chatRef,
        vditorTitle,
      };
      return <CustomChatContentRender {...propsData} />;
    },
    [vditorTitle],
  );

  const customHintBoxRender = useCallback((props: any) => {
    const propsData = {
      ...props,
      chatRef,
    };
    return <CustomHintBoxRender {...propsData} />;
  }, []);

  const feedBackConById = async (msg: Message, yesOrNot: boolean, feeBackType = 1) => {
    try {
      const data = {
        appCode,
        chatId,
        resDataId: msg.id as string,
        feeBackType,
        yesOrNot,
      };

      const likeStatus = {
        id: msg.id,
      } as Record<string, boolean | string>;
      const res = await feedBackContentById(data);
      if (res.data) {
        if (feeBackType === 1) {
          likeStatus.like = yesOrNot;
          if (yesOrNot) likeStatus.dislike = !yesOrNot;
        } else {
          likeStatus.dislike = yesOrNot;
          if (yesOrNot) likeStatus.like = !yesOrNot;
        }
      }

      // 更新仓库对话
      dispatchInUtils({
        type: 'chat/updateChatById',
        payload: {
          chatId,
          message: likeStatus,
        },
      });
    } catch (err: any) {
      Toast.error(err.msg || '操作失败');
    }
  };

  /** 点踩 */
  const onMessageBadFeedback = async (msg?: Message) => {
    if (!msg) return;
    await feedBackConById(msg, !msg.dislike, 2);
  };

  /** 点赞 */
  const onMessageGoodFeedback = async (msg?: Message) => {
    if (!msg) return;
    await feedBackConById(msg, !msg.like, 1);
  };
  const handleHintClick = (hint: string) => {
    handleMessageSend(hint, []);
    dispatchInUtils({
      type: 'chat/clearHints',
      payload: { chatId },
    });
  };

  return (
    <>
      <Chat
        ref={chatRef}
        className={styles.aiChat}
        chats={(chatMsg![chatId]?.messages || []) as Message[]}
        // hints={chatMsg![chatId]?.hints || []}
        onHintClick={handleHintClick}
        uploadProps={uploadProps}
        uploadTipProps={uploadTipProps}
        renderHintBox={customHintBoxRender}
        renderInputArea={customInputAreaRender}
        chatBoxRenderConfig={{
          renderChatBoxContent: customChatContentRender,
          renderChatBoxAction: customChatActionRender,
          renderChatBoxTitle: () => null,
        }}
        markdownRenderProps={{
          format: 'md',
          rehypePlugins: [rehypeMathJax],
          remarkPlugins: [remarkMath],
        }}
        onMessageBadFeedback={onMessageBadFeedback}
        onMessageGoodFeedback={onMessageGoodFeedback}
        showStopGenerate={false}
        roleConfig={chatRoleConfig}
        placeholder={placeholder || CHAT_PLACEHOLDER}
        enableUpload={false}
        topSlot={isTopSlot ? <GreetingRender /> : null}
        bottomSlot={<ChatBottomSlot />}
        sendButtonProps={{
          loading: chatMsg![chatId]?.pending,
        }}
        onMessageSend={handleSend}
        onStopGenerator={cancelRequest}
        onMessageReset={resetMessage}
        onMessageDelete={onMessageDelete}
      />
    </>
  );
});

export default AIChat;
